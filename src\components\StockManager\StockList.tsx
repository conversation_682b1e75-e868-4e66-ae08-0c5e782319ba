import { Trash2, TrendingUp, AlertTriangle, ExternalLink, RefreshCw, Activity, TrendingDown, GripVertical } from 'lucide-react';
import { Stock } from '@/types';
import { useBatchStockData, useBatchStockQuotes } from '@/hooks/useStockData';
import { detectVPattern } from '@/utils/patternDetection';
import { useMemo } from 'react';
import { MiniFlowChart } from '@/components/DataDisplay/MiniFlowChart';
import { formatMoneyAuto } from '@/utils/formatters';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface StockListProps {
  stocks: Stock[];
  onRemoveStock: (code: string) => void;
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  /** 是否显示实时监控数据 */
  showRealTimeData?: boolean;
  /** 是否全屏显示 */
  isFullScreen?: boolean;
  /** 拖拽排序回调 */
  onReorderStocks?: (activeId: string, overId: string) => void;
}

export function StockList({
  stocks,
  onRemoveStock,
  onSelectStock,
  selectedStock,
  showRealTimeData = false,
  isFullScreen = false,
  onReorderStocks
}: StockListProps) {
  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px 移动距离后才开始拖拽
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && onReorderStocks) {
      onReorderStocks(active.id as string, over?.id as string);
    }
  };

  // 获取股票代码列表
  const stockCodes = stocks.map(s => s.code);

  // 批量获取股票数据（仅在显示实时数据时）
  const {
    results,
    isLoading: dataLoading,
    isFetching,
    refetch
  } = useBatchStockData(stockCodes, 20, {
    refetchInterval: showRealTimeData ? 60000 : undefined, // 60秒自动刷新
    enabled: showRealTimeData && stockCodes.length > 0
  });

  // 批量获取股票行情数据（包含涨跌幅）
  const {
    data: quotesData
  } = useBatchStockQuotes(stockCodes, {
    refetchInterval: showRealTimeData ? 60000 : undefined, // 60秒自动刷新
    enabled: showRealTimeData && stockCodes.length > 0
  });

  // 处理股票数据，集成V字型识别、实时数据和行情数据
  const stocksWithData = useMemo(() => {
    if (!showRealTimeData || !results || Object.keys(results).length === 0) {
      return stocks.map(stock => ({
        ...stock,
        data: null,
        hasVPattern: false,
        latestFlow: 0,
        change24h: 0,
        quote: null,
        changePercent: 0
      }));
    }

    return stocks.map(stock => {
      const data = results[stock.code];
      const quote = quotesData?.results?.[stock.code] || null;
      const hasVPattern = data?.klines ? detectVPattern(data.klines).hasVPattern : false;

      // 计算最新流入和变化
      const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;
      const change24h = data?.klines && data.klines.length >= 2
        ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
        : 0;

      return {
        ...stock,
        data,
        quote,
        hasVPattern,
        latestFlow,
        change24h,
        changePercent: quote?.changePercent || 0,
      };
    });
  }, [stocks, results, quotesData, showRealTimeData]);
  if (stocks.length === 0) {
    return (
      <div className={`text-center ${isFullScreen ? 'py-16' : 'py-8'}`}>
        <TrendingUp className={`${isFullScreen ? 'w-20 h-20' : 'w-12 h-12'} text-gray-300 mx-auto mb-3`} />
        <p className={`text-gray-500 mb-2 ${isFullScreen ? 'text-lg' : ''}`}>暂无股票代码</p>
        <p className={`${isFullScreen ? 'text-base' : 'text-sm'} text-gray-400`}>请添加股票代码开始监控</p>
      </div>
    );
  }

  return (
    <div className="space-y-2 h-full flex flex-col">
      {/* 列表头部 */}
      <div className={`flex items-center justify-between ${isFullScreen ? 'text-base' : 'text-sm'} text-gray-600 pb-2 border-b border-gray-200`}>
        <div className="flex items-center gap-2">
          <span>股票代码 ({stocks.length})</span>
          {showRealTimeData && (
            <>
              {isFetching && (
                <RefreshCw className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'} animate-spin text-blue-500`} />
              )}
              <button
                onClick={() => refetch()}
                className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500 hover:text-gray-700 transition-colors`}
                disabled={isFetching}
                title="刷新实时数据"
              >
                <Activity className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'}`} />
              </button>
            </>
          )}
        </div>
        <span>操作</span>
      </div>

      {/* 股票列表 - 支持拖拽排序 */}
      <div className={`flex-1 ${isFullScreen ? 'overflow-hidden' : 'max-h-64 overflow-y-auto'}`}>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={stockCodes} strategy={verticalListSortingStrategy}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {stocksWithData.map((stock) => (
                <SortableStockListItem
                  key={stock.code}
                  stock={stock}
                  isSelected={selectedStock === stock.code}
                  onSelect={onSelectStock}
                  onRemove={onRemoveStock}
                  showRealTimeData={showRealTimeData}
                  stockData={stock.data}
                  hasVPattern={stock.hasVPattern}
                  latestFlow={stock.latestFlow}
                  change24h={stock.change24h}
                  quote={stock.quote}
                  isFullScreen={isFullScreen}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      </div>
      
      {/* 批量操作和状态信息 */}
      {stocks.length > 1 && (
        <div className="pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={() => {
                if (window.confirm('确定要清空所有股票代码吗？')) {
                  stocks.forEach(stock => onRemoveStock(stock.code));
                }
              }}
              className="text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1"
            >
              <AlertTriangle className="w-4 h-4" />
              清空所有
            </button>

            {showRealTimeData && (
              <div className="text-xs text-gray-500">
                {dataLoading ? '加载中...' : `实时监控已启用`}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface StockListItemProps {
  stock: Stock;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
  showRealTimeData?: boolean;
  stockData?: any;
  hasVPattern?: boolean;
  latestFlow?: number;
  change24h?: number;
  quote?: any;
  isFullScreen?: boolean;
  /** 拖拽手柄属性 */
  dragHandleProps?: any;
  /** 是否正在拖拽 */
  isDragging?: boolean;
}

// 可排序的股票列表项组件
function SortableStockListItem(props: StockListItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props.stock.code });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <StockListItem
        {...props}
        dragHandleProps={{ ...attributes, ...listeners }}
        isDragging={isDragging}
      />
    </div>
  );
}

function StockListItem({
  stock,
  isSelected,
  onSelect,
  onRemove,
  showRealTimeData = false,
  stockData,
  hasVPattern = false,
  latestFlow = 0,
  change24h = 0,
  quote,
  isFullScreen = false,
  dragHandleProps,
  isDragging = false
}: StockListItemProps) {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`确定要删除股票 ${stock.code} 吗？`)) {
      onRemove(stock.code);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(stock.code);
    }
  };

  const handleViewFlow = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `https://data.eastmoney.com/zjlx/${stock.code}.html`;
    window.open(url, '_blank');
  };

  // 获取中国股市颜色（红涨绿跌）
  const getChineseStockColor = (value: number) => {
    if (value > 0) {
      return 'text-red-600'; // 红色 - 上涨/流入
    } else if (value < 0) {
      return 'text-green-600'; // 绿色 - 下跌/流出
    } else {
      return 'text-gray-600'; // 灰色 - 无变化
    }
  };

  return (
    <div
      className={`
        flex items-center ${isFullScreen ? 'p-3' : 'p-2'} rounded-lg border transition-all duration-200
        ${isSelected
          ? 'border-primary-500 bg-primary-50'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
        ${onSelect ? 'cursor-pointer' : ''}
        ${isDragging ? 'shadow-lg scale-105' : ''}
        min-h-[48px]
      `}
      onClick={handleSelect}
    >
      {/* 拖拽手柄 */}
      {dragHandleProps && (
        <div
          {...dragHandleProps}
          className="flex items-center justify-center w-6 h-6 mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600 transition-colors"
          onClick={(e) => e.stopPropagation()}
        >
          <GripVertical className="w-4 h-4" />
        </div>
      )}
      {/* 股票基本信息 */}
      <div className="flex items-center gap-2 min-w-0 flex-shrink-0">
        {/* 股票代码和名称 */}
        <div className="flex items-center gap-2">
          <span className={`${isFullScreen ? 'text-sm' : 'text-xs'} font-mono text-gray-600`}>
            {stock.code}
          </span>
          <span className={`${isFullScreen ? 'text-sm' : 'text-xs'} font-medium ${
            isSelected ? 'text-primary-700' : 'text-gray-900'
          } truncate`}>
            {stock.name}
          </span>
        </div>

        {/* 选中指示器 */}
        {isSelected && (
          <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"></div>
        )}
      </div>

      {/* 实时监控数据 - 单行显示 */}
      {showRealTimeData && (
        <div className="flex items-center gap-3 flex-1 min-w-0 ml-3">
          {stockData && stockData.klines ? (
            <>
              {/* 主力净流入 */}
              <span
                className={`font-bold ${isFullScreen ? 'text-sm' : 'text-xs'} ${getChineseStockColor(latestFlow)} flex-shrink-0`}
              >
                {formatMoneyAuto(latestFlow)}
              </span>

              {/* 涨跌幅 */}
              {quote && quote.changePercent !== 0 && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  {quote.changePercent > 0 ? (
                    <TrendingUp className="w-3 h-3 text-red-500" />
                  ) : (
                    <TrendingDown className="w-3 h-3 text-green-500" />
                  )}
                  <span
                    className={`${isFullScreen ? 'text-xs' : 'text-xs'} font-medium ${getChineseStockColor(quote.changePercent)}`}
                  >
                    {quote.changePercent > 0 ? '+' : ''}{quote.changePercent.toFixed(2)}%
                  </span>
                </div>
              )}

              {/* 24小时变化 */}
              {change24h !== 0 && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  {change24h > 0 ? (
                    <TrendingUp className="w-3 h-3 text-red-500" />
                  ) : (
                    <TrendingDown className="w-3 h-3 text-green-500" />
                  )}
                  <span
                    className={`${isFullScreen ? 'text-xs' : 'text-xs'} font-medium ${getChineseStockColor(change24h)}`}
                  >
                    {change24h > 0 ? '+' : ''}{formatMoneyAuto(change24h)}
                  </span>
                </div>
              )}

              {/* V字形模式指示器 */}
              {hasVPattern && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  <Activity className="w-3 h-3 text-red-500" />
                  <span className="text-xs text-red-600 font-medium">V型</span>
                </div>
              )}

              {/* 迷你图表 */}
              <div className="w-16 h-6 flex-shrink-0 ml-auto">
                <MiniFlowChart
                  klines={stockData.klines}
                  height={24}
                  showVPattern={true}
                />
              </div>
            </>
          ) : (
            /* 无数据状态 */
            <div className="flex items-center text-gray-400 flex-1">
              <span className="text-xs">加载中...</span>
            </div>
          )}
        </div>
      )}

      {/* 操作按钮组 */}
      <div className="flex items-center gap-1 ml-2 flex-shrink-0">
        {/* 查看资金流向按钮 */}
        <button
          onClick={handleViewFlow}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
          title="查看资金流向"
        >
          <ExternalLink className="w-3 h-3" />
        </button>

        {/* 删除按钮 */}
        <button
          onClick={handleRemove}
          className="p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200"
          title="删除股票"
        >
          <Trash2 className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
}
